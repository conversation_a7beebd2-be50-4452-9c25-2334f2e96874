
/**
 * 统一高效导出系统 v1.0
 * 让所有页面都使用XLSX.js进行前端导出，实现exportData()一样的高效体验
 */

// 确保XLSX库已加载
if (typeof XLSX === 'undefined') {
    console.error('❌ XLSX.js库未加载，请先引入XLSX库');
}

/**
 * 判断是否为时间字段
 */
function isTimeField(fieldName, value) {
    // 根据字段名判断
    const timeFieldNames = ['time', 'date', 'created_at', 'updated_at', 'release_time', 'create_time'];
    const isTimeFieldName = timeFieldNames.some(name => 
        fieldName.toLowerCase().includes(name.toLowerCase())
    );
    
    // 根据值格式判断（扩展的时间格式检测）
    const timePatterns = [
        /^\d{4}-\d{2}-\d{2}[\sT]\d{2}:\d{2}:\d{2}/,  // 2025-01-06 14:30:00 或 2025-01-06T14:30:00
        /^\w{3}, \d{2} \w{3} \d{4}/,                  // Wed, 06 Aug 2025 03:05:38
        /^\d{4}\/\d{2}\/\d{2}/,                       // 2025/01/06
        /^\d{2}\/\d{2}\/\d{4}/                        // 06/01/2025
    ];
    const isTimeValue = timePatterns.some(pattern => pattern.test(value));
    
    return isTimeFieldName || isTimeValue;
}

/**
 * 清理时间格式，使其Excel友好
 */
function cleanTimeFormat(timeString) {
    if (!timeString || typeof timeString !== 'string') {
        return timeString;
    }
    
    try {
        // 尝试解析各种时间格式并转换为标准格式
        let date;
        
        // 处理英文日期格式：Wed, 06 Aug 2025 03:05:38
        if (timeString.includes(',') && /\w{3}, \d{2} \w{3} \d{4}/.test(timeString)) {
            date = new Date(timeString);
        }
        // 处理标准ISO格式：2025-07-03T11:09:07.6Z
        else if (timeString.includes('T') || timeString.includes('-')) {
            date = new Date(timeString);
        }
        // 其他格式尝试直接解析
        else {
            date = new Date(timeString);
        }
        
        // 如果解析成功，格式化为Excel友好格式
        if (!isNaN(date.getTime())) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
        
        // 如果解析失败，使用原始的字符串处理方式
        return timeString
            .replace(/\.\d+$/, '')  // 移除末尾毫秒
            .replace('T', ' ')      // 将ISO格式的T替换为空格
            .replace(/Z$/, '')      // 移除末尾的Z
            .substring(0, 19);      // 确保长度为19位
            
    } catch (error) {
        console.warn('时间格式转换失败:', timeString, error);
        return timeString; // 转换失败则返回原值
    }
}

/**
 * 统一导出功能 - 高效前端导出
 * @param {Array} data - 要导出的数据数组
 * @param {Array} columns - 列定义数组 [{ field: 'LOT_ID', header: '内部工单号' }]
 * @param {string} filename - 文件名（不含扩展名）
 * @param {string} sheetName - 工作表名称
 * @param {Function} onSuccess - 成功回调
 * @param {Function} onError - 错误回调
 */
function unifiedExportData(data, columns, filename, sheetName = 'Sheet1', onSuccess = null, onError = null) {
    try {
        console.log(`🚀 开始统一导出: ${filename}, 数据量: ${data.length}`);
        
        if (!data || data.length === 0) {
            const message = '没有数据可导出';
            if (onError) onError(new Error(message));
            else alert(message);
            return;
        }
        
        // 检查XLSX库
        if (typeof XLSX === 'undefined') {
            const message = 'XLSX.js库未加载，无法导出Excel文件';
            if (onError) onError(new Error(message));
            else alert(message);
            return;
        }
        
        // 转换数据格式 - 根据列定义重新组织数据
        const exportData = data.map(row => {
            const newRow = {};
            columns.forEach(col => {
                let value = row[col.field] || '';
                
                // 🕒 Excel友好时间格式处理
                if (typeof value === 'string' && isTimeField(col.field, value)) {
                    value = cleanTimeFormat(value);
                }
                
                newRow[col.header] = value;
            });
            return newRow;
        });
        
        // 使用XLSX.js生成Excel文件
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(exportData);
        
        // 设置列宽自适应
        const colWidths = columns.map(col => ({
            wch: Math.max(col.header.length, 15) // 最小15字符宽度
        }));
        ws['!cols'] = colWidths;
        
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
        
        // 生成文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:\-T]/g, '');
        const fullFilename = `${filename}_${timestamp}.xlsx`;
        
        // 导出文件
        XLSX.writeFile(wb, fullFilename);
        
        const message = `导出成功！文件名: ${fullFilename}`;
        console.log(`✅ ${message}`);
        
        if (onSuccess) onSuccess(fullFilename);
        else if (typeof showNotification === 'function') {
            showNotification('success', message);
        } else {
            alert(message);
        }
        
    } catch (error) {
        console.error('❌ 统一导出失败:', error);
        const message = `导出失败: ${error.message}`;
        
        if (onError) onError(error);
        else if (typeof showNotification === 'function') {
            showNotification('error', message);
        } else {
            alert(message);
        }
    }
}

/**
 * 从API获取数据并导出 - 适用于需要实时获取数据的场景
 * @param {string} apiUrl - API接口地址
 * @param {Array} columns - 列定义
 * @param {string} filename - 文件名
 * @param {string} sheetName - 工作表名称
 * @param {Object} params - API请求参数
 */
function unifiedExportFromAPI(apiUrl, columns, filename, sheetName = 'Sheet1', params = {}) {
    console.log(`🔍 从API获取数据并导出: ${apiUrl}`);
    
    // 显示加载状态
    const loadingMessage = '正在获取数据并准备导出...';
    if (typeof showNotification === 'function') {
        showNotification('info', loadingMessage);
    }
    
    // 构建请求URL
    const url = new URL(apiUrl, window.location.origin);
    Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
            url.searchParams.append(key, params[key]);
        }
    });
    
    fetch(url.toString())
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(result => {
            if (result.success && result.data) {
                console.log(`✅ 从API获取到 ${result.data.length} 条数据`);
                unifiedExportData(result.data, columns, filename, sheetName);
            } else {
                throw new Error(result.error || '获取数据失败');
            }
        })
        .catch(error => {
            console.error('❌ 从API导出失败:', error);
            const message = `从API导出失败: ${error.message}`;
            if (typeof showNotification === 'function') {
                showNotification('error', message);
            } else {
                alert(message);
            }
        });
}

/**
 * 预定义的导出配置 - 常用页面的导出列定义
 */
const EXPORT_CONFIGS = {
    // 失败批次导出配置
    failed_lots: {
        columns: [
            { field: 'LOT_ID', header: '内部工单号' },
            { field: 'STAGE', header: '工序' },
            { field: 'DEVICE', header: '产品名称' },
            { field: 'LOT_TYPE', header: '工单分类' },
            { field: 'PKG_PN', header: '封装形式' },
            { field: 'GOOD_QTY', header: '良品数量' },
            { field: 'failure_reason', header: '失败原因' },
            { field: 'suggestion', header: '建议解决方案' },
            { field: 'timestamp', header: '创建时间' }
        ],
        filename: '失败批次清单',
        sheetName: '失败批次'
    },
    
    // 订单数据导出配置
    orders: {
        columns: [
            { field: 'order_id', header: '订单编号' },
            { field: 'product_name', header: '产品名称' },
            { field: 'quantity', header: '数量' },
            { field: 'stage', header: '工序' },
            { field: 'status', header: '状态' },
            { field: 'create_time', header: '创建时间' }
        ],
        filename: '订单数据',
        sheetName: '订单清单'
    },
    
    // 通用批次导出配置
    lots: {
        columns: [
            { field: 'LOT_ID', header: '内部工单号' },
            { field: 'DEVICE', header: '产品名称' },
            { field: 'CHIP_ID', header: '芯片名称' },
            { field: 'GOOD_QTY', header: '良品数量' },
            { field: 'STAGE', header: '工序' },
            { field: 'WIP_STATE', header: '状态' },
            { field: 'CREATE_TIME', header: '创建时间' }
        ],
        filename: '批次数据',
        sheetName: '批次清单'
    }
};

/**
 * 使用预定义配置导出
 * @param {string} configKey - 配置键名
 * @param {Array} data - 数据数组
 * @param {Object} overrides - 覆盖配置
 */
function unifiedExportWithConfig(configKey, data, overrides = {}) {
    const config = EXPORT_CONFIGS[configKey];
    if (!config) {
        const message = `找不到导出配置: ${configKey}`;
        console.error(`❌ ${message}`);
        alert(message);
        return;
    }
    
    const finalConfig = { ...config, ...overrides };
    unifiedExportData(data, finalConfig.columns, finalConfig.filename, finalConfig.sheetName);
}

// 导出到全局作用域
window.unifiedExportData = unifiedExportData;
window.unifiedExportFromAPI = unifiedExportFromAPI;
window.unifiedExportWithConfig = unifiedExportWithConfig;
window.EXPORT_CONFIGS = EXPORT_CONFIGS;

console.log('✅ 统一导出系统已加载');
