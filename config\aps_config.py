#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS平台统一配置管理器
合并三套配置系统的最佳实践，提供简化而完整的配置管理

设计原则：
1. 配置加载优先级：config.ini > 环境变量 > 默认值
2. 支持开发环境和exe打包环境
3. 保持向后兼容性
4. 简化配置验证逻辑
"""

import os
import sys
import configparser
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv

logger = logging.getLogger(__name__)


class ApsConfig:
    """APS平台统一配置管理器"""
    
    _instance = None
    _lock = None
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            if cls._lock is None:
                import threading
                cls._lock = threading.Lock()
            
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        
        return cls._instance
    
    def __init__(self):
        """初始化配置管理器"""
        if hasattr(self, '_initialized') and self._initialized:
            return
        
        self._initialized = True
        self.base_dir = Path(__file__).parent.parent
        
        # 加载配置（按优先级）
        self._load_dotenv_file()
        self._load_external_config()
        self._init_all_configs()
        self._validate_critical_configs()
    
    def _load_dotenv_file(self):
        """加载.env文件"""
        env_file = self.base_dir / '.env'
        if env_file.exists():
            load_dotenv(env_file)
            logger.debug(f"✅ .env文件已加载: {env_file}")
        else:
            logger.debug(f"⚠️ .env文件不存在: {env_file}")
    
    def _load_external_config(self):
        """加载外部config.ini文件（exe环境支持）"""
        self.external_config = configparser.ConfigParser()
        
        # 确定config.ini路径
        if getattr(sys, 'frozen', False):
            # exe环境：从exe同目录读取
            config_path = Path(sys.executable).parent / 'config.ini'
        else:
            # 开发环境：从项目根目录读取
            config_path = self.base_dir / 'config.ini'
        
        if config_path.exists():
            try:
                # 尝试多种编码方式
                encodings = ['utf-8-sig', 'utf-8', 'gbk', 'ascii']
                for encoding in encodings:
                    try:
                        self.external_config.read(config_path, encoding=encoding)
                        logger.info(f"✅ 外部配置文件已加载: {config_path} (编码: {encoding})")
                        return
                    except Exception:
                        continue
                
                logger.warning(f"⚠️ 无法读取外部配置文件: {config_path}")
            except Exception as e:
                logger.warning(f"⚠️ 加载外部配置文件失败: {e}")
        else:
            logger.debug(f"外部配置文件不存在: {config_path}")
    
    def _get_config_value(self, key: str, section: str = 'DATABASE', default: Any = None, value_type: type = str):
        """获取配置值（优先级：config.ini > 环境变量 > 默认值）"""
        # 1. 尝试从外部配置文件读取
        try:
            if self.external_config.has_section(section):
                config_value = None
                if value_type == int:
                    config_value = self.external_config.getint(section, key, fallback=None)
                elif value_type == bool:
                    config_value = self.external_config.getboolean(section, key, fallback=None)
                elif value_type == float:
                    config_value = self.external_config.getfloat(section, key, fallback=None)
                else:
                    config_value = self.external_config.get(section, key, fallback=None)
                
                # 只有当配置值不为None时才返回
                if config_value is not None:
                    return config_value
        except Exception:
            pass
        
        # 2. 尝试从环境变量读取
        env_value = os.environ.get(key.upper())
        if env_value is not None:
            try:
                if value_type == int:
                    return int(env_value)
                elif value_type == bool:
                    return env_value.lower() in ('true', '1', 'yes', 'on')
                elif value_type == float:
                    return float(env_value)
                else:
                    return env_value
            except ValueError:
                logger.warning(f"环境变量 {key.upper()} 类型转换失败: {env_value}")
        
        # 3. 返回默认值
        return default
    
    def _init_all_configs(self):
        """初始化所有配置项"""
        
        # ==============================================
        # 数据库配置
        # ==============================================
        self.DB_HOST = self._get_config_value('host', 'DATABASE', 'localhost')
        self.DB_PORT = self._get_config_value('port', 'DATABASE', 3306, int)
        self.DB_USER = self._get_config_value('username', 'DATABASE', 'root')  # config.ini中是username
        self.DB_PASSWORD = self._get_config_value('password', 'DATABASE', 'WWWwww123!')
        self.DB_NAME = self._get_config_value('database', 'DATABASE', 'aps')
        self.DB_CHARSET = self._get_config_value('charset', 'DATABASE', 'utf8mb4')
        
        # MySQL兼容性别名
        self.MYSQL_HOST = self.DB_HOST
        self.MYSQL_PORT = self.DB_PORT
        self.MYSQL_USER = self.DB_USER
        self.MYSQL_PASSWORD = self.DB_PASSWORD
        self.MYSQL_CHARSET = self.DB_CHARSET
        
        # ==============================================
        # Flask应用配置
        # ==============================================
        self.FLASK_HOST = self._get_config_value('host', 'APPLICATION', '0.0.0.0')
        self.FLASK_PORT = self._get_config_value('port', 'APPLICATION', 5000, int)
        self.SECRET_KEY = self._get_config_value('secret_key', 'APPLICATION', 'dev-secret-key-change-in-production')
        self.DEBUG = self._get_config_value('debug', 'APPLICATION', True, bool)
        self.FLASK_ENV = 'development' if self.DEBUG else 'production'
        
        # ==============================================
        # 数据库连接池配置 - 策略A：Flask-SQLAlchemy为主
        # ==============================================
        # Flask-SQLAlchemy连接池配置 - 服务主应用
        self.DB_POOL_SIZE = self._get_config_value('pool_size', 'DATABASE', 40, int) or 40  # 基础连接数
        self.DB_MAX_OVERFLOW = self._get_config_value('max_overflow', 'DATABASE', 60, int) or 60  # 溢出连接数
        self.DB_POOL_TIMEOUT = self._get_config_value('pool_timeout', 'DATABASE', 30, int) or 30  # 获取连接超时
        self.DB_POOL_RECYCLE = self._get_config_value('pool_recycle', 'DATABASE', 3600, int) or 3600  # 连接回收时间
        
        # 自定义连接池配置 - 服务工具脚本
        self.CUSTOM_POOL_MIN_SIZE = self._get_config_value('custom_pool_min_size', 'DATABASE', 10, int) or 10
        self.CUSTOM_POOL_INIT_SIZE = self._get_config_value('custom_pool_init_size', 'DATABASE', 20, int) or 20
        self.CUSTOM_POOL_MAX_SIZE = self._get_config_value('custom_pool_max_size', 'DATABASE', 50, int) or 50
        
        # ==============================================
        # 系统配置
        # ==============================================
        self.TIMEZONE = self._get_config_value('timezone', 'SYSTEM', 'Asia/Shanghai')
        self.DEFAULT_PAGE_SIZE = self._get_config_value('page_size', 'SYSTEM', 1000, int)
        self.DEFAULT_UPH = self._get_config_value('default_uph', 'SYSTEM', 1000, int)
        self.MAX_WORKERS = self._get_config_value('max_workers', 'SYSTEM', 10, int)
        
        # ==============================================
        # 文件路径配置
        # ==============================================
        self.LOG_DIR = self._get_config_value('log_dir', 'PATHS', 'logs')
        self.UPLOAD_DIR = self._get_config_value('upload_dir', 'PATHS', 'uploads')
        self.DOWNLOAD_DIR = self._get_config_value('download_dir', 'PATHS', 'downloads')
        self.INSTANCE_DIR = self._get_config_value('instance_dir', 'PATHS', 'instance')
        self.STATIC_EXPORTS_DIR = self._get_config_value('static_exports_dir', 'PATHS', 'static/exports')
        
        # ==============================================
        # Redis配置（可选）
        # ==============================================
        self.REDIS_HOST = self._get_config_value('redis_host', 'CACHE', 'localhost')
        self.REDIS_PORT = self._get_config_value('redis_port', 'CACHE', 6379, int)
        self.REDIS_DB = self._get_config_value('redis_db', 'CACHE', 0, int)
        self.REDIS_PASSWORD = self._get_config_value('redis_password', 'CACHE', '')
        
        # ==============================================
        # 日志配置
        # ==============================================
        self.LOG_LEVEL = self._get_config_value('log_level', 'LOGGING', 'INFO')
        self.QUIET_STARTUP = self._get_config_value('quiet_startup', 'LOGGING', False, bool)
        
        # ==============================================
        # 管理员配置
        # ==============================================
        self.ADMIN_USERNAME = self._get_config_value('admin_username', 'ADMIN', 'admin')
        self.ADMIN_PASSWORD = self._get_config_value('admin_password', 'ADMIN', 'admin')
        
        # ==============================================
        # 应用信息配置
        # ==============================================
        # 🏷️ 从统一版本号管理中心获取版本信息
        try:
            from app.services import __version__, APP_VERSION
            default_version = APP_VERSION
        except ImportError:
            # 降级方案：使用默认值
            default_version = '2.3.3'
        
        self.APP_VERSION = self._get_config_value('app_version', 'APPLICATION', default_version)
        self.APP_NAME = self._get_config_value('app_name', 'APPLICATION', 'APS平台')
        
        # ==============================================
        # API配置（兼容性）
        # ==============================================
        self.API_PREFIX = self._get_config_value('api_prefix', 'API', '/api')
        self.API_VERSION = self._get_config_value('api_version', 'API', 'v2')
        self.API_HOST = self._get_config_value('api_host', 'API', self.FLASK_HOST)
        self.API_PORT = self._get_config_value('api_port', 'API', self.FLASK_PORT, int)
        self.API_BASE_URL = self._get_config_value('api_base_url', 'API', f'http://{self.FLASK_HOST}:{self.FLASK_PORT}')
        self.API_V2_PREFIX = self._get_config_value('api_v2_prefix', 'API', '/api/v2')
        self.API_V1_PREFIX = self._get_config_value('api_v1_prefix', 'API', '/api/v1')
        
        # API功能开关
        self.API_CORS_ENABLED = self._get_config_value('api_cors_enabled', 'API', True, bool)
        self.API_AUTH_ENABLED = self._get_config_value('api_auth_enabled', 'API', False, bool)
        self.API_RATE_LIMIT_ENABLED = self._get_config_value('api_rate_limit_enabled', 'API', False, bool)
        self.API_DOCS_ENABLED = self._get_config_value('api_docs_enabled', 'API', True, bool)
        
        # API详细配置
        self.API_TIMEOUT = self._get_config_value('api_timeout', 'API', 30, int)
        self.API_MAX_CONTENT_LENGTH = self._get_config_value('api_max_content_length', 'API', 16 * 1024 * 1024, int)
        self.API_TOKEN_EXPIRE_HOURS = self._get_config_value('api_token_expire_hours', 'API', 24, int)
        self.API_REFRESH_TOKEN_EXPIRE_DAYS = self._get_config_value('api_refresh_token_expire_days', 'API', 30, int)
        
        # API CORS配置
        self.API_CORS_ORIGINS = self._get_config_value('api_cors_origins', 'API', ['*'])
        if isinstance(self.API_CORS_ORIGINS, str):
            self.API_CORS_ORIGINS = [origin.strip() for origin in self.API_CORS_ORIGINS.split(',')]
        self.API_CORS_METHODS = self._get_config_value('api_cors_methods', 'API', ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])
        if isinstance(self.API_CORS_METHODS, str):
            self.API_CORS_METHODS = [method.strip() for method in self.API_CORS_METHODS.split(',')]
        
        # API限流配置
        self.API_RATE_LIMIT_DEFAULT = self._get_config_value('api_rate_limit_default', 'API', '100/hour')
        self.API_RATE_LIMIT_STORAGE_URL = self._get_config_value('api_rate_limit_storage_url', 'API', 'memory://')
    
    def _validate_critical_configs(self):
        """验证关键配置"""
        errors = []
        
        # 验证数据库配置
        if not self.DB_PASSWORD:
            errors.append("数据库密码未设置")
        
        if not (1 <= self.DB_PORT <= 65535):
            errors.append(f"数据库端口无效: {self.DB_PORT}")
        
        if not (1024 <= self.FLASK_PORT <= 65535):
            errors.append(f"Flask端口无效: {self.FLASK_PORT}")
        
        # 验证必要目录
        for dir_path in [self.LOG_DIR, self.UPLOAD_DIR, self.DOWNLOAD_DIR, self.INSTANCE_DIR]:
            try:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
            except Exception as e:
                errors.append(f"无法创建目录 {dir_path}: {e}")
        
        if errors:
            error_msg = "配置验证失败:\n" + "\n".join(f"  - {error}" for error in errors)
            logger.error(error_msg)
            if not self.DEBUG:  # 生产环境严格验证
                raise ValueError(error_msg)
        else:
            logger.info("✅ 配置验证通过")
    
    # ==============================================
    # 属性方法（兼容性）
    # ==============================================
    
    @property
    def DATABASE_URI(self) -> str:
        """构建数据库连接URI"""
        return f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}?charset={self.DB_CHARSET}"
    
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        """Flask-SQLAlchemy数据库URI"""
        return self.DATABASE_URI
    
    @property
    def SQLALCHEMY_ENGINE_OPTIONS(self) -> Dict[str, Any]:
        """Flask-SQLAlchemy引擎选项"""
        return {
            'pool_size': self.DB_POOL_SIZE,
            'max_overflow': self.DB_MAX_OVERFLOW,
            'pool_timeout': self.DB_POOL_TIMEOUT,
            'pool_recycle': self.DB_POOL_RECYCLE,
            'pool_pre_ping': True,
            'echo': False  # 生产环境关闭SQL日志
        }
    
    @property
    def REDIS_URL(self) -> str:
        """Redis连接URL"""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    def get_log_file_path(self, filename: str = 'app.log') -> Path:
        """获取日志文件路径"""
        log_dir = Path(self.LOG_DIR)
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir / filename
    
    def get(self, key: str, default: Any = None) -> Any:
        """向后兼容的get方法，用于替代dict风格的配置访问"""
        # 映射常用的配置键
        key_mapping = {
            'MYSQL_HOST': 'DB_HOST',
            'MYSQL_PORT': 'DB_PORT',
            'MYSQL_USER': 'DB_USER',
            'MYSQL_PASSWORD': 'DB_PASSWORD',
            'MYSQL_DATABASE': 'DB_NAME',
            'MYSQL_CHARSET': 'DB_CHARSET',
            'MYSQL_SYSTEM_DATABASE': 'DB_NAME',  # 系统数据库也使用主数据库
        }
        
        # 转换键名
        attr_name = key_mapping.get(key, key)
        
        # 获取属性值
        if hasattr(self, attr_name):
            return getattr(self, attr_name)
        else:
            return default
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要（用于调试）"""
        return {
            'flask_host': self.FLASK_HOST,
            'flask_port': self.FLASK_PORT,
            'db_host': self.DB_HOST,
            'db_port': self.DB_PORT,
            'db_name': self.DB_NAME,
            'debug': self.DEBUG,
            'log_level': self.LOG_LEVEL,
            'pool_size': self.DB_POOL_SIZE,
            'max_overflow': self.DB_MAX_OVERFLOW
        }


# ==============================================
# 全局配置实例和兼容性接口
# ==============================================

# 全局配置实例
config = ApsConfig()

# 向后兼容的函数接口
def get_config() -> ApsConfig:
    """获取配置实例（向后兼容）"""
    return config

def get_unified_config() -> ApsConfig:
    """获取统一配置实例（兼容unified_config）"""
    return config

# Flask配置类（用于app.config.from_object()）
class FlaskConfig:
    """Flask配置类"""
    
    def __init__(self, aps_config: ApsConfig = None):
        if aps_config is None:
            aps_config = config
        
        # 基础配置
        self.SECRET_KEY = aps_config.SECRET_KEY
        self.DEBUG = aps_config.DEBUG
        
        # 数据库配置
        self.SQLALCHEMY_DATABASE_URI = aps_config.SQLALCHEMY_DATABASE_URI
        self.SQLALCHEMY_ENGINE_OPTIONS = aps_config.SQLALCHEMY_ENGINE_OPTIONS
        self.SQLALCHEMY_TRACK_MODIFICATIONS = False
        
        # 其他Flask配置
        self.MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
        self.PERMANENT_SESSION_LIFETIME = 8 * 3600  # 8小时
        
        # 应用版本信息（Flask模板需要）
        # 🏷️ 从统一版本号管理中心获取版本信息
        try:
            from app.services import APP_VERSION as SERVICES_APP_VERSION
            self.APP_VERSION = SERVICES_APP_VERSION
        except ImportError:
            # 降级方案：使用默认值
            self.APP_VERSION = '2.3.3'
        self.APP_NAME = 'APS平台'
        
        # 其他可能缺失的配置项
        self.TIMEZONE = aps_config.TIMEZONE
        self.DEFAULT_PAGE_SIZE = aps_config.DEFAULT_PAGE_SIZE


if __name__ == '__main__':
    """测试配置加载"""
    print("🔧 APS统一配置管理器测试")
    print("=" * 50)
    
    test_config = ApsConfig()
    
    print("📋 配置摘要:")
    summary = test_config.get_config_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    print(f"\n🌐 连接信息:")
    print(f"  Flask: {test_config.FLASK_HOST}:{test_config.FLASK_PORT}")
    print(f"  数据库: {test_config.DATABASE_URI}")
    
    if test_config.REDIS_HOST != 'localhost' or test_config.REDIS_PASSWORD:
        print(f"  Redis: {test_config.REDIS_URL}")
    
    print(f"\n📁 关键路径:")
    print(f"  日志目录: {test_config.LOG_DIR}")
    print(f"  上传目录: {test_config.UPLOAD_DIR}")
    print(f"  下载目录: {test_config.DOWNLOAD_DIR}")