#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS平台 - 高性能数据库连接池管理器
支持100+用户并发，具备动态扩容和智能监控功能
"""

import pymysql
import logging
import threading
import time
import os
from typing import Dict, Any, Optional
from contextlib import contextmanager
from queue import Queue, Empty
from config.aps_config import config

logger = logging.getLogger(__name__)

class DatabaseConnectionPool:
    """高性能数据库连接池管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._config = None
        self._config_loaded_time = 0
        self._config_ttl = 300  # 配置缓存5分钟
        
        # 动态连接池配置 - 策略A：服务工具脚本，配置从aps_config统一管理
        from config.aps_config import config
        self._min_pool_size = config.CUSTOM_POOL_MIN_SIZE  # 最小连接数
        self._pool_size = config.CUSTOM_POOL_INIT_SIZE  # 初始连接数  
        self._max_connections = config.CUSTOM_POOL_MAX_SIZE  # 最大连接数
        self._connection_timeout = config.DB_POOL_TIMEOUT  # 连接超时
        self._pool_timeout = 10  # 获取连接超时
        
        # 动态扩容配置
        self._scale_up_threshold = 0.8  # 80%使用率时扩容
        self._scale_down_threshold = 0.3  # 30%使用率时缩容
        self._scale_check_interval = 30  # 30秒检查一次
        self._last_scale_check = 0
        
        # 连接池字典：{database_name: Queue}
        self._pools: Dict[str, Queue] = {}
        self._pool_locks: Dict[str, threading.Lock] = {}
        self._connection_counts: Dict[str, int] = {}
        self._active_connections: Dict[str, int] = {}  # 活跃连接数
        
        # 连接健康检查
        self._health_check_interval = 300  # 5分钟检查一次
        self._last_health_check = 0
        self._unhealthy_connections = set()
        
        # 统计信息
        self._stats = {
            'connections_created': 0,
            'connections_reused': 0,
            'connections_scaled_up': 0,
            'connections_scaled_down': 0,
            'config_cache_hits': 0,
            'config_cache_misses': 0,
            'pool_timeouts': 0,
            'connection_errors': 0,
            'health_checks_performed': 0,
            'unhealthy_connections_removed': 0
        }
        
        # 启动监控线程
        self._monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitoring_thread.start()
        
        logger.info(f"🔄 高性能数据库连接池已初始化 - 初始:{self._pool_size}, 最大:{self._max_connections}")
    
    def _monitoring_loop(self):
        """连接池监控循环"""
        while True:
            try:
                current_time = time.time()
                
                # 动态扩缩容检查
                if current_time - self._last_scale_check > self._scale_check_interval:
                    self._check_and_scale_pools()
                    self._last_scale_check = current_time
                
                # 健康检查
                if current_time - self._last_health_check > self._health_check_interval:
                    self._perform_health_check()
                    self._last_health_check = current_time
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 连接池监控异常: {e}")
                time.sleep(30)  # 异常时等待30秒
    
    def _check_and_scale_pools(self):
        """检查并动态调整连接池大小"""
        for database in list(self._pools.keys()):
            try:
                pool = self._pools[database]
                active = self._active_connections.get(database, 0)
                total = self._connection_counts.get(database, 0)
                
                if total == 0:
                    continue
                
                usage_rate = active / total
                
                # 扩容检查
                if usage_rate > self._scale_up_threshold and total < self._max_connections:
                    scale_up_count = min(10, self._max_connections - total)  # 每次最多扩容10个
                    self._scale_up_pool(database, scale_up_count)
                
                # 缩容检查
                elif usage_rate < self._scale_down_threshold and total > self._min_pool_size:
                    scale_down_count = min(5, total - self._min_pool_size)  # 每次最多缩容5个
                    self._scale_down_pool(database, scale_down_count)
                    
            except Exception as e:
                logger.error(f"❌ 连接池扩缩容检查失败 {database}: {e}")
    
    def _scale_up_pool(self, database: str, count: int):
        """扩容连接池"""
        try:
            with self._pool_locks[database]:
                for _ in range(count):
                    if self._connection_counts[database] >= self._max_connections:
                        break
                    
                    connection = self._create_connection(database)
                    self._pools[database].put_nowait(connection)
                    self._connection_counts[database] += 1
                    self._stats['connections_scaled_up'] += 1
                
                logger.info(f"📈 连接池扩容: {database} +{count} (总计:{self._connection_counts[database]})")
                
        except Exception as e:
            logger.error(f"❌ 连接池扩容失败 {database}: {e}")
    
    def _scale_down_pool(self, database: str, count: int):
        """缩容连接池"""
        try:
            with self._pool_locks[database]:
                removed = 0
                for _ in range(count):
                    try:
                        connection = self._pools[database].get_nowait()
                        connection.close()
                        self._connection_counts[database] -= 1
                        removed += 1
                        self._stats['connections_scaled_down'] += 1
                    except Empty:
                        break
                
                if removed > 0:
                    logger.info(f"📉 连接池缩容: {database} -{removed} (总计:{self._connection_counts[database]})")
                    
        except Exception as e:
            logger.error(f"❌ 连接池缩容失败 {database}: {e}")
    
    def _perform_health_check(self):
        """执行连接健康检查"""
        for database in list(self._pools.keys()):
            try:
                pool = self._pools[database]
                unhealthy_count = 0
                healthy_connections = []
                
                # 检查池中的连接
                temp_connections = []
                while not pool.empty():
                    try:
                        connection = pool.get_nowait()
                        temp_connections.append(connection)
                    except Empty:
                        break
                
                # 验证连接健康状态
                for connection in temp_connections:
                    try:
                        connection.ping(reconnect=False)
                        healthy_connections.append(connection)
                    except:
                        unhealthy_count += 1
                        try:
                            connection.close()
                        except:
                            pass
                        self._connection_counts[database] -= 1
                        self._stats['unhealthy_connections_removed'] += 1
                
                # 将健康连接放回池中
                for connection in healthy_connections:
                    try:
                        pool.put_nowait(connection)
                    except:
                        try:
                            connection.close()
                        except:
                            pass
                        self._connection_counts[database] -= 1
                
                if unhealthy_count > 0:
                    logger.warning(f"🏥 健康检查: {database} 移除{unhealthy_count}个不健康连接")
                
                self._stats['health_checks_performed'] += 1
                
            except Exception as e:
                logger.error(f"❌ 健康检查失败 {database}: {e}")

    def _get_cached_config(self) -> Dict[str, Any]:
        """获取缓存的数据库配置"""
        current_time = time.time()
        
        if (self._config is None or 
            current_time - self._config_loaded_time > self._config_ttl):
            
            # 配置过期或首次加载，重新读取
            try:
                self._config = {
            'host': config.DB_HOST,
            'port': config.DB_PORT,
            'user': config.DB_USER,
            'password': config.DB_PASSWORD,
            'database': config.DB_NAME,
            'charset': config.DB_CHARSET
        }
                self._config_loaded_time = current_time
                self._stats['config_cache_misses'] += 1
                logger.debug("🔄 重新加载数据库配置")
            except Exception as e:
                logger.error(f"❌ 配置加载失败: {e}")
                raise
        else:
            self._stats['config_cache_hits'] += 1
        
        return self._config
    
    def _create_connection(self, database: str = 'aps') -> pymysql.Connection:
        """创建新的数据库连接"""
        config = self._get_cached_config()
        
        conn_params = {
            'host': config['host'],
            'port': config['port'],
            'user': config['user'],
            'password': config['password'],
            'database': database,
            'charset': config['charset'],
            'autocommit': False,
            'connect_timeout': self._connection_timeout,
            'read_timeout': 60,  # 读取超时60秒
            'write_timeout': 60,  # 写入超时60秒
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        try:
            connection = pymysql.connect(**conn_params)
            self._stats['connections_created'] += 1
            logger.debug(f"✅ 新建数据库连接: {config['host']}:{config['port']}/{database}")
            return connection
        except Exception as e:
            self._stats['connection_errors'] += 1
            logger.error(f"❌ 数据库连接创建失败: {e}")
            raise
    
    def _get_pool(self, database: str) -> Queue:
        """获取指定数据库的连接池"""
        if database not in self._pools:
            with self._lock:
                if database not in self._pools:
                    self._pools[database] = Queue(maxsize=self._max_connections)
                    self._pool_locks[database] = threading.Lock()
                    self._connection_counts[database] = 0
                    self._active_connections[database] = 0
                    
                    # 预创建初始连接
                    for _ in range(self._min_pool_size):
                        try:
                            connection = self._create_connection(database)
                            self._pools[database].put_nowait(connection)
                            self._connection_counts[database] += 1
                        except Exception as e:
                            logger.error(f"❌ 预创建连接失败 {database}: {e}")
                            break
                    
                    logger.info(f"🔄 为数据库 '{database}' 创建连接池，预创建 {self._connection_counts[database]} 个连接")
        
        return self._pools[database]

    def get_connection(self, database: str = 'aps') -> pymysql.Connection:
        """从连接池获取数据库连接（高性能版本）"""
        pool = self._get_pool(database)
        connection = None
        
        # 尝试从池中获取连接
        try:
            connection = pool.get_nowait()
            
            # 快速健康检查
            try:
                connection.ping(reconnect=False)
                self._active_connections[database] += 1
                self._stats['connections_reused'] += 1
                logger.debug(f"♻️ 复用数据库连接: {database}")
                return connection
            except:
                # 连接失效，关闭并创建新连接
                try:
                    connection.close()
                except:
                    pass
                self._connection_counts[database] -= 1
                connection = None
                
        except Empty:
            # 池中没有可用连接
            pass
        
        # 需要创建新连接
        with self._pool_locks[database]:
            if self._connection_counts[database] >= self._max_connections:
                # 等待可用连接
                try:
                    connection = pool.get(timeout=self._pool_timeout)
                    connection.ping(reconnect=False)
                    self._active_connections[database] += 1
                    self._stats['connections_reused'] += 1
                    return connection
                except (Empty, Exception) as e:
                    self._stats['pool_timeouts'] += 1
                    raise Exception(f"数据库连接池已满，无法获取新连接: {database} (活跃:{self._active_connections[database]}, 总数:{self._connection_counts[database]})")
            
            # 创建新连接
            connection = self._create_connection(database)
            self._connection_counts[database] += 1
            self._active_connections[database] += 1
            return connection

    def return_connection(self, connection: pymysql.Connection, database: str = 'aps'):
        """将连接归还到连接池（优化版本）"""
        if connection is None:
            return
        
        try:
            # 减少活跃连接计数
            if database in self._active_connections:
                self._active_connections[database] = max(0, self._active_connections[database] - 1)
            
            # 检查连接是否还有效
            connection.ping(reconnect=False)
            
            # 清理事务状态
            try:
                connection.rollback()  # 确保事务清洁
            except:
                pass
            
            pool = self._get_pool(database)
            
            try:
                pool.put_nowait(connection)
                logger.debug(f"🔄 连接已归还到池: {database}")
            except:
                # 池已满，关闭连接
                connection.close()
                with self._pool_locks[database]:
                    self._connection_counts[database] -= 1
                logger.debug(f"🗑️ 连接池已满，关闭连接: {database}")
                
        except Exception as e:
            # 连接有问题，直接关闭
            try:
                connection.close()
            except:
                pass
            
            with self._pool_locks[database]:
                if self._connection_counts[database] > 0:
                    self._connection_counts[database] -= 1
            
            logger.debug(f"🗑️ 问题连接已关闭: {database}")
    
    @contextmanager
    def get_cursor(self, database: str = 'aps', autocommit: bool = False):
        """获取数据库游标的上下文管理器"""
        connection = None
        cursor = None
        
        try:
            connection = self.get_connection(database)
            if autocommit:
                connection.autocommit(True)
            
            cursor = connection.cursor()
            yield cursor
            
            if not autocommit:
                connection.commit()
                
        except Exception as e:
            if connection and not autocommit:
                try:
                    connection.rollback()
                except:
                    pass
            logger.error(f"❌ 数据库操作失败: {e}")
            raise
            
        finally:
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            
            if connection:
                try:
                    if autocommit:
                        connection.autocommit(False)  # 恢复默认设置
                    self.return_connection(connection, database)
                except:
                    # 如果归还失败，强制关闭
                    try:
                        connection.close()
                    except:
                        pass
    
    @contextmanager 
    def get_connection_context(self, database: str = 'aps'):
        """获取数据库连接的上下文管理器"""
        connection = None
        
        try:
            connection = self.get_connection(database)
            yield connection
        finally:
            if connection:
                self.return_connection(connection, database)
    
    def close_all_pools(self):
        """关闭所有连接池"""
        logger.info("🔄 关闭所有数据库连接池...")
        
        for database, pool in self._pools.items():
            while not pool.empty():
                try:
                    connection = pool.get_nowait()
                    connection.close()
                except:
                    pass
            
            self._connection_counts[database] = 0
        
        self._pools.clear()
        self._pool_locks.clear()
        self._connection_counts.clear()
        
        logger.info("✅ 所有数据库连接池已关闭")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        stats = self._stats.copy()
        stats['pools'] = {}
        
        for database in self._pools:
            stats['pools'][database] = {
                'active_connections': self._connection_counts.get(database, 0),
                'pool_size': self._pools[database].qsize(),
                'max_connections': self._max_connections
            }
        
        return stats
    
    def print_stats(self):
        """打印连接池统计信息"""
        stats = self.get_stats()
        
        print("📊 数据库连接池统计:")
        print(f"  新建连接: {stats['connections_created']}")
        print(f"  复用连接: {stats['connections_reused']}")
        print(f"  配置缓存命中: {stats['config_cache_hits']}")
        print(f"  配置缓存未命中: {stats['config_cache_misses']}")
        
        if stats['pools']:
            print("  连接池状态:")
            for db, pool_stats in stats['pools'].items():
                print(f"    {db}: 活跃{pool_stats['active_connections']}/池内{pool_stats['pool_size']}/最大{pool_stats['max_connections']}")


# 全局连接池实例
_connection_pool = None
_pool_lock = threading.Lock()

def get_connection_pool() -> DatabaseConnectionPool:
    """获取全局连接池实例（单例）"""
    global _connection_pool
    if _connection_pool is None:
        with _pool_lock:
            if _connection_pool is None:
                _connection_pool = DatabaseConnectionPool()
    return _connection_pool

# 便捷方法
def get_db_connection(database: str = 'aps') -> pymysql.Connection:
    """获取数据库连接（便捷方法）"""
    return get_connection_pool().get_connection(database)

def get_db_cursor(database: str = 'aps', autocommit: bool = False):
    """获取数据库游标上下文管理器（便捷方法）"""
    return get_connection_pool().get_cursor(database, autocommit)

def get_db_connection_context(database: str = 'aps'):
    """获取数据库连接上下文管理器（便捷方法）"""
    return get_connection_pool().get_connection_context(database)

def return_db_connection(connection: pymysql.Connection, database: str = 'aps'):
    """归还数据库连接（便捷方法）"""
    return get_connection_pool().return_connection(connection, database)

def print_connection_stats():
    """打印连接池统计信息（便捷方法）"""
    return get_connection_pool().print_stats() 