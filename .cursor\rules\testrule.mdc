---
alwaysApply: false
---
# 测试规则框架 - 强制性规范

## 📋 规范说明

**创建时间**: 2025-01-16  
**版本**: 1.0  
**适用范围**: 所有功能模块的测试方案设计  
**执行要求**: **强制遵循，不得简化或跳过**

---

## 🎯 规范目标

**解决问题**: 测试方案设计时经常出现不完善的地方，对真实使用场景考虑不足  
**核心目标**: 确保每次测试方案设计都能覆盖所有真实使用场景，减少生产环境bug

---

## 🏗️ 完整测试规则框架

### 📐 6维度测试设计（必须全覆盖）

#### 1. 🔧 基础功能测试维度
- **✅ 单元测试**: 每个函数/方法的基本功能验证
- **✅ 集成测试**: 模块间交互和数据流测试
- **✅ 端到端测试**: 完整业务流程测试
- **✅ API接口测试**: 所有API的输入输出验证

#### 2. 💾 数据库连接池专项测试维度
**（重点强化维度，基于历史问题）**

##### A. 连接池基础功能测试
```python
def test_connection_pool_basic():
    """连接池基础功能测试"""
    # ✅ 连接获取和释放
    # ✅ 连接池初始化
    # ✅ 连接有效性检查
    # ✅ 连接池大小限制
```

##### B. 高并发压力测试
```python
def test_connection_pool_concurrent():
    """高并发场景测试"""
    # ✅ 100个并发连接请求
    # ✅ 连接池满时的等待机制
    # ✅ 连接泄漏检测
    # ✅ 内存使用监控
```

##### C. 异常场景测试
```python
def test_connection_pool_failure_scenarios():
    """异常场景恢复测试"""
    # ✅ 数据库服务重启时的重连
    # ✅ 网络中断恢复
    # ✅ 超时连接处理
    # ✅ 事务回滚测试
```

##### D. 生产环境模拟测试
```python
def test_connection_pool_production_simulation():
    """生产环境模拟测试"""
    # ✅ 24小时连续运行测试
    # ✅ 1000+用户并发模拟
    # ✅ 峰值流量冲击测试
    # ✅ 内存泄漏长期监控
```

#### 3. 🎭 真实使用场景测试维度

##### A. 数据量真实性测试
- **✅ 小数据量测试**: 10-100条记录（开发环境）
- **✅ 中等数据量测试**: 1000-10000条记录（测试环境）
- **✅ 大数据量测试**: 50000+条记录（生产环境模拟）
- **✅ 极限数据量测试**: 系统承载上限测试

##### B. 用户行为真实性测试
```python
def test_realistic_user_behavior():
    """真实用户行为模拟"""
    # ✅ 频繁刷新页面
    # ✅ 快速连续点击操作
    # ✅ 长时间页面停留
    # ✅ 多标签页同时操作
    # ✅ 异常网络环境（慢网络、断网重连）
```

##### C. 业务场景完整性测试
```python
def test_complete_business_scenarios():
    """完整业务场景测试"""
    # ✅ 正常排产流程（从数据导入到结果导出）
    # ✅ 异常数据处理（缺失字段、格式错误）
    # ✅ 并发排产冲突处理
    # ✅ 长时间运行稳定性
    # ✅ 数据回滚和恢复
```

#### 4. ⚡ 性能测试维度

##### A. 响应时间测试
- **✅ API响应时间**: <2秒 (正常), <5秒 (峰值)
- **✅ 页面加载时间**: <3秒 (首次), <1秒 (缓存)
- **✅ 数据库查询时间**: <500ms (简单), <2秒 (复杂)
- **✅ 排产算法执行时间**: <10秒 (1000批次)

##### B. 系统资源使用测试
```python
def test_system_resource_usage():
    """系统资源使用监控"""
    # ✅ CPU使用率 < 80%
    # ✅ 内存使用 < 1GB
    # ✅ 数据库连接数 < 50
    # ✅ 磁盘IO监控
```

#### 5. 🛡️ 故障恢复测试维度

##### A. 服务故障恢复测试
```python
def test_service_failure_recovery():
    """服务故障恢复测试"""
    # ✅ MySQL服务重启恢复
    # ✅ Redis服务故障降级
    # ✅ 应用服务异常重启
    # ✅ 网络分区恢复
```

##### B. 数据一致性保障测试
```python
def test_data_consistency():
    """数据一致性测试"""
    # ✅ 并发写入冲突处理
    # ✅ 事务回滚完整性
    # ✅ 缓存与数据库同步
    # ✅ 分布式锁机制
```

#### 6. 🔍 历史问题回归测试
**（基于项目实际问题的防护测试）**

```python
def test_historical_issues_regression():
    """历史问题回归测试"""
    
    # ✅ 双圈圈加载问题回归测试
    # ✅ 数据库字段缺失问题测试
    # ✅ 路由冲突问题测试
    # ✅ 排产重复记录问题测试
    # ✅ 页面筛选失效问题测试
    # ✅ 配置缺失导致的排产失败测试
    # ✅ UPH配置不匹配问题测试
    # ✅ 设备类型缺失问题测试
```

---

## 🎯 强制性检查清单

**设计任何测试方案前，必须逐项检查以下清单：**

### ✅ 基础检查项（4项）
- [ ] 是否覆盖了所有API端点？
- [ ] 是否包含了正常和异常输入测试？
- [ ] 是否有完整的错误处理测试？
- [ ] 是否包含了边界值测试？

### ✅ 数据库测试检查项（4项）
- [ ] 是否测试了连接池的并发性能？
- [ ] 是否测试了连接超时和重连机制？
- [ ] 是否测试了事务的完整性？
- [ ] 是否包含了长时间运行的稳定性测试？

### ✅ 真实场景检查项（4项）
- [ ] 测试数据量是否接近生产环境？
- [ ] 是否模拟了真实的用户操作模式？
- [ ] 是否考虑了网络延迟和中断场景？
- [ ] 是否测试了系统在压力下的表现？

### ✅ 性能测试检查项（4项）
- [ ] 是否设定了明确的性能基准？
- [ ] 是否监控了系统资源使用情况？
- [ ] 是否测试了缓存机制的有效性？
- [ ] 是否包含了内存泄漏检测？

### ✅ 故障恢复检查项（4项）
- [ ] 是否测试了各种服务故障场景？
- [ ] 是否验证了数据完整性和一致性？
- [ ] 是否测试了系统的自动恢复能力？
- [ ] 是否包含了人工介入的恢复流程？

**总计：20个强制检查项，全部通过才能开始实施测试**

---

## 🚀 测试方案设计流程

### 1. 设计前强制检查
```
测试需求分析 → 对照20项检查清单 → 6维度测试设计 → 实施验证 → 回归测试
```

### 2. 强制性问题确认
**设计任何测试方案前，必须回答以下问题：**

- ❓ **真实性**: 测试数据量是否接近生产环境？
- ❓ **完整性**: 是否覆盖了正常、异常、边界情况？  
- ❓ **并发性**: 是否测试了多用户同时使用的场景？
- ❓ **稳定性**: 是否包含了长时间运行的测试？
- ❓ **恢复性**: 是否测试了故障后的自动恢复？
- ❓ **回归性**: 是否防止了历史问题的重现？

**如果任何一个问题的答案是"否"，则测试方案不完整，必须补充完善。**

---

## 📊 数据库连接池测试特别要求

**基于历史问题，数据库连接池测试必须特别强化：**

### 并发性能要求
- **并发连接数**: 最少100个并发连接测试
- **响应时间**: 平均连接时间 < 100ms
- **成功率**: 并发测试成功率 > 99%

### 稳定性要求
- **长期运行**: 至少30分钟连续测试（或快速模拟24小时）
- **内存泄漏**: 内存增长 < 100MB
- **连接泄漏**: 测试前后连接数变化 < 5个

### 异常恢复要求
- **数据库重启**: 能自动重连并恢复正常
- **网络中断**: 中断恢复后连接池正常工作
- **事务回滚**: 异常时能正确回滚，不影响后续操作

---

## 🎯 性能基准要求

### 强制性性能指标
- **API响应时间**: 
  - 正常负载: < 2秒
  - 峰值负载: < 5秒
- **页面加载时间**: 
  - 首次加载: < 3秒
  - 缓存加载: < 1秒
- **数据库查询**: 
  - 简单查询: < 500ms
  - 复杂查询: < 2秒
- **系统资源**: 
  - CPU使用率: < 80%
  - 内存使用: < 1GB
  - 数据库连接: < 50个

### 压力测试要求
- **用户并发数**: 最少20个并发用户
- **数据量级**: 
  - 开发环境: 100条记录
  - 测试环境: 10000条记录
  - 生产模拟: 50000条记录

---

## 📝 测试实施示例

### 完整测试方案结构
```python
class ComprehensiveTestSuite(unittest.TestCase):
    """
    遵循测试规则框架的完整测试套件
    必须包含所有6个维度的测试
    """
    
    # 1. 基础功能测试维度
    def test_basic_functionality(self):
        pass
    
    # 2. 数据库连接池专项测试维度
    def test_database_connection_pool(self):
        pass
    
    # 3. 真实使用场景测试维度
    def test_realistic_scenarios(self):
        pass
    
    # 4. 性能测试维度
    def test_performance_requirements(self):
        pass
    
    # 5. 故障恢复测试维度
    def test_failure_recovery(self):
        pass
    
    # 6. 历史问题回归测试
    def test_historical_issues_regression(self):
        pass
```

### 测试命名规范
- **基础功能**: `test_basic_*`
- **数据库连接池**: `test_connection_pool_*`
- **真实场景**: `test_realistic_*`
- **性能测试**: `test_performance_*`
- **故障恢复**: `test_failure_*`
- **历史回归**: `test_regression_*`

---

## ⚠️ 违规处理

### 不遵循规范的后果
1. **测试方案被退回**: 必须按规范重新设计
2. **代码审查不通过**: 影响开发进度
3. **生产环境问题**: 可能导致严重的系统故障

### 规范检查机制
1. **设计阶段**: 对照20项检查清单逐项验证
2. **实施阶段**: 确保6个维度全部覆盖
3. **审查阶段**: 代码审查时强制检查规范遵循情况

---

## 🎉 规范价值

### 预期效果
- ✅ **杜绝测试不完善**: 强制性检查清单防止遗漏
- ✅ **真实场景全覆盖**: 测试条件接近生产环境
- ✅ **历史问题不重现**: 系统性回归测试防护
- ✅ **生产环境稳定**: 提前发现性能和稳定性问题

### 使用收益
- **开发阶段**: 提前发现问题，减少返工
- **测试阶段**: 全面验证，提高质量
- **生产阶段**: 稳定运行，用户满意

---

## 📞 规范执行

**本规范为强制性要求，所有测试方案设计必须严格遵循。**

**如有疑问或需要支持，请参考：**
- 📋 完整示例: `test_connection_pool_comprehensive.py`
- 📚 详细文档: `docs/完整测试规则框架设计文档.md`
- 🔧 项目README: 测试框架章节

---
