"""
API v3路由 - 使用动态字段管理器
实施迁移的第一步，提供新的API接口
"""

from flask import Blueprint, request, jsonify, render_template
import logging
from datetime import datetime
# 临时禁用enhanced_data_source_manager（语法错误待修复）
# from app.services.enhanced_data_source_manager import get_enhanced_manager

# 临时替代方案：简化的数据管理器
class SimpleDataManager:
    """临时替代enhanced_data_source_manager的简化版本"""
    
    def __init__(self):
        # 初始化field_manager属性，确保与原始管理器兼容
        try:
            from app.services.dynamic_field_manager import get_field_manager
            self.field_manager = get_field_manager()
        except Exception as e:
            # 如果field_manager不可用，创建一个简单的替代品
            print(f"⚠️ field_manager初始化失败: {e}")
            self.field_manager = self._create_simple_field_manager()
    
    def _create_simple_field_manager(self):
        """创建简单的字段管理器替代品"""
        class SimpleFieldManager:
            def get_table_fields(self, table_name):
                # 返回基本字段信息（临时实现）
                return []
            
            def get_table_info(self, table_name):
                # 返回基本表信息（临时实现）
                return {
                    'fields': [],
                    'primary_key': 'id',
                    'database': 'aps'
                }
        
        return SimpleFieldManager()
    
    def get_table_data(self, table_name, **kwargs):
        """获取表数据 - 修复：确保导出数据与页面显示数据一致"""
        try:
            # 提取参数
            page = kwargs.get('page', 1)
            per_page = kwargs.get('per_page', 50)
            filters = kwargs.get('filters', [])
            sort_by = kwargs.get('sort_by', 'id')
            sort_order = kwargs.get('sort_order', 'ASC')
            load_all = kwargs.get('load_all', False)
            

            
            # 对于eqp_status表，使用DataSourceManager但应用筛选逻辑
            if table_name.lower() in ['eqp_status']:
                from app.services.data_source_manager import DataSourceManager
                data_manager = DataSourceManager()
                
                # 使用与页面显示相同的数据源和处理逻辑
                equipment_data, data_source = data_manager.get_equipment_status_data()
                
                if isinstance(equipment_data, dict):
                    data = list(equipment_data.values())
                else:
                    data = equipment_data if equipment_data else []
                
                
                # 应用筛选条件
                if filters:
                    filtered_data = []
                    for record in data:
                        # 检查记录是否满足所有筛选条件
                        matches_all_filters = True
                        for filter_item in filters:
                            if isinstance(filter_item, dict):
                                field = filter_item.get('field')
                                operator = filter_item.get('operator', 'equals')
                                value = filter_item.get('value')
                                
                                if field and value and field != '_global_search':
                                    record_value = str(record.get(field, '')).lower()
                                    filter_value = str(value).lower()
                                    
                                    if operator == 'contains':
                                        if filter_value not in record_value:
                                            matches_all_filters = False
                                            break
                                    elif operator == 'equals':
                                        if record_value != filter_value:
                                            matches_all_filters = False
                                            break
                                    elif operator == 'starts_with':
                                        if not record_value.startswith(filter_value):
                                            matches_all_filters = False
                                            break
                                    elif operator == 'ends_with':
                                        if not record_value.endswith(filter_value):
                                            matches_all_filters = False
                                            break
                                    elif operator == 'not_equals':
                                        if record_value == filter_value:
                                            matches_all_filters = False
                                            break
                        
                        if matches_all_filters:
                            filtered_data.append(record)
                    
                    data = filtered_data
                
                # 应用分页逻辑
                total = len(data)
                if not load_all and total > 0:
                    start = (page - 1) * per_page
                    end = start + per_page
                    paged_data = data[start:end]
                else:
                    paged_data = data
                
                # 生成列信息（与页面显示一致）
                columns = []
                if paged_data:
                    first_record = paged_data[0]
                    # 排除调试字段，只显示业务字段
                    columns = [k for k in first_record.keys() if k not in ['available', 'raw_data']]
                

                
                return {
                    'success': True,
                    'data': paged_data,
                    'total': total,
                    'columns': columns,
                    'page': page,
                    'per_page': per_page,
                    'load_mode': 'all' if load_all else 'paged',
                    'data_source': data_source
                }
            
            # 删除eqp_status表的特殊处理，统一使用标准数据库查询方式
            # 原有逻辑处理其他表
            from app import db
            from sqlalchemy import text
            
            # 🔧 如果load_all为True，设置大的per_page
            if load_all:
                per_page = 10000
                page = 1
            
            # 构建基础查询
            base_query = f"SELECT * FROM {table_name}"
            count_query_base = f"SELECT COUNT(*) as total FROM {table_name}"
            
            # 处理筛选条件（简化版本）
            where_conditions = []
            query_params = {}
            
            if filters:
                for i, filter_item in enumerate(filters):
                    if isinstance(filter_item, dict):
                        field = filter_item.get('field')
                        operator = filter_item.get('operator', 'equals')
                        value = filter_item.get('value')
                        
                        if field and value and field != '_global_search':
                            param_name = f"param_{i}"
                            if operator == 'contains':
                                where_conditions.append(f"{field} LIKE :{param_name}")
                                query_params[param_name] = f"%{value}%"
                            elif operator == 'equals':
                                where_conditions.append(f"{field} = :{param_name}")
                                query_params[param_name] = value
            
            # 添加WHERE子句
            if where_conditions:
                where_clause = " WHERE " + " AND ".join(where_conditions)
                base_query += where_clause
                count_query_base += where_clause
            
            # 添加排序
            if sort_by and sort_by != '_global_search':
                base_query += f" ORDER BY {sort_by} {sort_order}"
            
            # 添加分页
            offset = (page - 1) * per_page
            base_query += " LIMIT :limit OFFSET :offset"
            query_params['limit'] = per_page
            query_params['offset'] = offset
            
            # 执行数据查询
            result = db.session.execute(text(base_query), query_params)
            data = [dict(row._mapping) for row in result.fetchall()]
            
            # 执行计数查询
            count_result = db.session.execute(text(count_query_base), {k: v for k, v in query_params.items() if k not in ['limit', 'offset']})
            total = count_result.scalar()
            
            return {
                'success': True,
                'data': data,
                'total': total,
                'page': page,
                'per_page': per_page,
                'load_mode': 'all' if load_all else 'paged'
            }
            
        except Exception as e:
            print(f"🔧 SimpleDataManager查询失败: {e}")  # 调试输出
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'total': 0
            }
    
    def get_table_columns(self, table_name):
        """获取表列信息 - 临时实现"""
        try:
            from app import db
            from sqlalchemy import text
            
            query = text(f"DESCRIBE {table_name}")
            result = db.session.execute(query)
            
            # 🔧 修复：直接提取字段名，而不是返回RowMapping对象
            columns = []
            column_details = []
            
            for row in result.fetchall():
                row_dict = dict(row._mapping)
                field_name = row_dict.get('Field')
                if field_name:
                    columns.append(field_name)  # 字段名列表
                    column_details.append(row_dict)  # 完整字段信息
            
            return {
                'success': True,
                'columns': columns,  # 返回字段名列表（兼容existing code）
                'column_details': column_details  # 返回完整字段信息（供需要的地方使用）
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'columns': []
            }
    
    def create_record(self, table_name, data):
        """创建记录 - 临时实现"""
        return {'success': False, 'error': '临时管理器不支持创建记录'}
    
    def update_record(self, table_name, data):
        """更新记录 - 临时实现"""
        return {'success': False, 'error': '临时管理器不支持更新记录'}
    
    def delete_record(self, table_name, record_id):
        """删除记录 - 临时实现"""
        return {'success': False, 'error': '临时管理器不支持删除记录'}

# 全局单例实例
_simple_manager_instance = None

def get_enhanced_manager():
    """临时替代函数，返回简化的数据管理器"""
    global _simple_manager_instance
    if _simple_manager_instance is None:
        _simple_manager_instance = SimpleDataManager()
        
        # 🔧 确保field_manager属性存在（双重保险）
        if not hasattr(_simple_manager_instance, 'field_manager'):
            print("⚠️ 紧急修复：为SimpleDataManager添加field_manager属性")
            try:
                from app.services.dynamic_field_manager import get_field_manager
                _simple_manager_instance.field_manager = get_field_manager()
            except Exception as e:
                print(f"⚠️ field_manager紧急修复失败: {e}")
                # 创建最简单的替代品
                class EmptyFieldManager:
                    def get_table_fields(self, table_name): return []
                    def get_table_info(self, table_name): return {'fields': [], 'primary_key': 'id', 'database': 'aps'}
                _simple_manager_instance.field_manager = EmptyFieldManager()
    
    return _simple_manager_instance
# 临时禁用dynamic_field_manager（语法错误待修复）
# from app.services.dynamic_field_manager import get_field_manager
from app.config.table_configs import get_table_config, get_field_type_config
import os

logger = logging.getLogger(__name__)

# 创建API v3蓝图
api_v3 = Blueprint('api_v3', __name__, url_prefix='/api/v3')

@api_v3.route('/tables', methods=['GET'])
def get_supported_tables():
    """获取支持的表列表 - 动态发现"""
    try:
        manager = get_enhanced_manager()
        result = manager.get_supported_tables()
        
        logger.info(f"✅ API v3: 获取支持表列表成功")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API v3: 获取支持表列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/columns', methods=['GET'])
def get_table_columns_v3(table_name):
    """获取表字段信息 - 动态发现"""
    try:
        manager = get_enhanced_manager()
        result = manager.get_table_columns(table_name)
        
        if result['success']:
            logger.info(f"✅ API v3: 获取表字段成功 - {table_name}: {len(result['columns'])}个字段")
        else:
            logger.warning(f"⚠️ API v3: 获取表字段失败 - {table_name}: {result.get('error')}")
            
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API v3: 获取表字段异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/info', methods=['GET'])
def get_table_info_v3(table_name):
    """获取表完整信息 - 包含字段、类型、配置等"""
    try:
        manager = get_enhanced_manager()
        table_info = manager.field_manager.get_table_info(table_name)

        if table_info:
            logger.info(f"✅ API v3: 获取表信息成功 - {table_name}")
            return jsonify({
                'success': True,
                'table_name': table_name,
                'table_info': table_info
            })
        else:
            return jsonify({
                'success': False,
                'error': f'表不存在: {table_name}'
            }), 404

    except Exception as e:
        logger.error(f"❌ API v3: 获取表信息失败 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/structure', methods=['GET'])
def get_table_structure_v3(table_name):
    """获取表结构信息 - 用于前端动态渲染"""
    try:
        # 获取表配置
        from app.config.table_configs import get_table_config
        table_config = get_table_config(table_name)

        # 使用enhanced_data_source_manager获取字段
        manager = get_enhanced_manager()
        columns_result = manager.get_table_columns(table_name)

        if not columns_result.get('success', False):
            return jsonify({
                'success': False,
                'error': f'无法获取表字段: {table_name}'
            }), 404

        # 获取字段列表
        raw_columns = columns_result.get('columns', [])
        hidden_fields = table_config.get('hidden_fields', [])
        field_types = table_config.get('field_types', {})

        # 构建前端需要的columns格式
        columns = []

        for field_name in raw_columns:
            # 对于id字段，即使在hidden_fields中也要包含（但标记为hidden）
            if field_name in hidden_fields and field_name != 'id':
                continue

            column = {
                'name': field_name,
                'display_name': field_name,
                'type': field_types.get(field_name, 'text'),
                'nullable': field_name != 'id',  # id字段通常不可为空
                'primary_key': field_name == 'id',
                'hidden': field_name in hidden_fields
            }
            columns.append(column)

        logger.info(f"✅ API v3: 获取表结构成功 - {table_name}, 字段数: {len(columns)}")
        
        # 🔧 调试：检查数据类型，确保JSON可序列化
        response_data = {
            'success': True,
            'table_name': str(table_name),
            'columns': columns,  # 这应该是字典列表
            'primary_key': 'id',
            'display_name': str(table_config.get('title', table_name))
        }
        
        # 验证columns中每个元素都可以JSON序列化
        import json
        for i, col in enumerate(columns):
            try:
                json.dumps(col)
            except Exception as col_error:
                logger.error(f"❌ Column {i} 序列化失败: {col_error}, 内容: {col}")
                # 清理不可序列化的部分
                safe_col = {
                    'name': str(col.get('name', '')),
                    'display_name': str(col.get('display_name', '')),
                    'type': str(col.get('type', 'text')),
                    'nullable': bool(col.get('nullable', True)),
                    'primary_key': bool(col.get('primary_key', False)),
                    'hidden': bool(col.get('hidden', False))
                }
                columns[i] = safe_col
        
        # 重新构建安全的响应数据
        response_data['columns'] = columns
        
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"❌ API v3: 获取表结构失败 - {table_name}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/data', methods=['GET'])
def get_table_data_v3(table_name):
    """获取表数据 - 动态字段支持，支持按需加载"""
    try:
        # 获取请求参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        load_all = request.args.get('load_all', 'false').lower() == 'true'  # 是否加载全量数据

        # 处理筛选条件
        filters = []
        filters_param = request.args.get('filters')
        if filters_param:
            import json
            filters = json.loads(filters_param)

        # 处理排序条件
        sort_by = request.args.get('sort_by', '')
        sort_order = request.args.get('sort_order', 'asc')

        # 处理搜索条件
        search = request.args.get('search', '')
        if search:
            # 添加全文搜索到筛选条件
            filters.append({
                'field': '_global_search',
                'operator': 'contains',
                'value': search
            })

        manager = get_enhanced_manager()
        result = manager.get_table_data(table_name, page=page, per_page=per_page, filters=filters, sort_by=sort_by, sort_order=sort_order, load_all=load_all)

        if result['success']:
            load_mode = result.get('load_mode', 'paged')
            logger.info(f"✅ API v3: 获取表数据成功 - {table_name}: 总计{result['total']}条记录, 返回{len(result['data'])}条 ({load_mode}模式)")
        else:
            logger.warning(f"⚠️ API v3: 获取表数据失败 - {table_name}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ API v3: 获取表数据异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



@api_v3.route('/tables/<table_name>/data', methods=['POST'])
def create_record_v3(table_name):
    """创建记录 - 动态字段支持"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少数据'
            }), 400
        
        manager = get_enhanced_manager()
        result = manager.create_record(table_name, data)
        
        if result['success']:
            logger.info(f"✅ API v3: 创建记录成功 - {table_name}: ID {result.get('record_id')}")
        else:
            logger.warning(f"⚠️ API v3: 创建记录失败 - {table_name}: {result.get('error')}")
            
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API v3: 创建记录异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/data/<record_id>', methods=['PUT'])
def update_record_v3(table_name, record_id):
    """更新记录 - 动态字段支持"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少数据'
            }), 400

        # 获取表信息以确定正确的主键字段
        manager = get_enhanced_manager()
        table_info = manager.field_manager.get_table_info(table_name)

        if table_info:
            primary_key = table_info.get('primary_key', 'id')
        else:
            primary_key = 'id'  # 默认主键

        # 添加记录ID到数据中，使用正确的主键字段名
        data[primary_key] = record_id

        result = manager.update_record(table_name, data)

        if result['success']:
            logger.info(f"✅ API v3: 更新记录成功 - {table_name}: {result.get('affected_rows')}行")
        else:
            logger.warning(f"⚠️ API v3: 更新记录失败 - {table_name}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ API v3: 更新记录异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/data/<record_id>', methods=['DELETE'])
def delete_record_v3(table_name, record_id):
    """删除记录"""
    try:
        manager = get_enhanced_manager()
        result = manager.delete_record(table_name, record_id)

        if result['success']:
            logger.info(f"✅ API v3: 删除记录成功 - {table_name}: ID {record_id}")
        else:
            logger.warning(f"⚠️ API v3: 删除记录失败 - {table_name}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ API v3: 删除记录异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/data/batch', methods=['DELETE'])
def batch_delete_records_v3(table_name):
    """批量删除记录 - 动态字段支持"""
    try:
        data = request.get_json()
        if not data or 'ids' not in data:
            return jsonify({
                'success': False,
                'error': '缺少要删除的记录ID列表'
            }), 400

        ids = data['ids']
        if not isinstance(ids, list) or len(ids) == 0:
            return jsonify({
                'success': False,
                'error': 'ID列表不能为空'
            }), 400

        manager = get_enhanced_manager()
        result = manager.batch_delete_records(table_name, ids)

        if result['success']:
            logger.info(f"✅ API v3: 批量删除成功 - {table_name}: {result.get('deleted_count')}条记录")
        else:
            logger.warning(f"⚠️ API v3: 批量删除失败 - {table_name}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ API v3: 批量删除异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/validate', methods=['GET'])
def validate_table_mapping_v3(table_name):
    """验证表字段映射"""
    try:
        field_manager = get_field_manager()
        result = field_manager.validate_field_mapping(table_name)
        
        logger.info(f"✅ API v3: 验证字段映射 - {table_name}: {result.get('match_rate', 0)}%匹配")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API v3: 验证字段映射异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/config/tables/<table_name>', methods=['POST'])
def override_table_config_v3(table_name):
    """覆盖表配置"""
    try:
        config = request.get_json()
        if not config:
            return jsonify({
                'success': False,
                'error': '缺少配置数据'
            }), 400
        
        manager = get_enhanced_manager()
        result = manager.override_table_config(table_name, config)
        
        logger.info(f"✅ API v3: 覆盖表配置 - {table_name}")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API v3: 覆盖表配置异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/cache/clear', methods=['POST'])
def clear_cache_v3():
    """清理缓存"""
    try:
        manager = get_enhanced_manager()
        manager.clear_cache()
        
        logger.info("✅ API v3: 清理缓存成功")
        return jsonify({
            'success': True,
            'message': '缓存已清理'
        })
        
    except Exception as e:
        logger.error(f"❌ API v3: 清理缓存失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/migration/status', methods=['GET'])
def get_migration_status():
    """获取迁移状态"""
    try:
        field_manager = get_field_manager()
        
        # 检查配置文件状态
        config_exists = os.path.exists(field_manager.config_path)
        
        # 获取支持的表
        supported_tables = field_manager.get_supported_tables()
        
        status = {
            'config_file_exists': config_exists,
            'config_path': field_manager.config_path,
            'total_tables': len(supported_tables),
            'v3_api_active': True,
            'cache_size': len(field_manager.cache),
            'migration_ready': config_exists and len(supported_tables) > 0
        }
        
        logger.info(f"✅ API v3: 迁移状态检查 - 配置存在: {config_exists}, 表数: {status['total_tables']}")
        return jsonify({
            'success': True,
            'status': status
        })
        
    except Exception as e:
        logger.error(f"❌ API v3: 获取迁移状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/migration/test-page')
def migration_test_page():
    """迁移测试页面"""
    try:
        logger.info("✅ API v3: 迁移测试页面访问")
        return render_template('resources/migration_test.html')
        
    except Exception as e:
        logger.error(f"❌ API v3: 迁移测试页面访问失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/page/<table_name>')
def universal_resource_page(table_name):
    """通用资源管理页面 - API v3版本"""
    try:
        # 验证表是否存在
        manager = get_enhanced_manager()
        supported_tables_result = manager.get_supported_tables()
        
        if not supported_tables_result['success']:
            logger.warning(f"❌ API v3: 无法获取支持表列表")
            return f"""
            <html><head><title>系统错误</title></head>
            <body style="font-family: Arial; padding: 50px; text-align: center;">
                <h1>❌ 系统错误</h1>
                <p>无法获取支持的表列表</p>
                <a href="/api/v3/migration/test-page">返回测试页面</a>
            </body></html>
            """, 500
        
        # 获取表名列表
        supported_table_names = [t['table_name'] for t in supported_tables_result['tables']]
        
        if table_name not in supported_table_names:
            logger.warning(f"❌ API v3: 表 {table_name} 不存在或不支持")
            return f"""
            <html><head><title>表不存在</title></head>
            <body style="font-family: Arial; padding: 50px; text-align: center;">
                <h1>❌ 表不存在</h1>
                <p>表 '{table_name}' 不存在或不支持</p>
                                 <p>支持的表: {', '.join(supported_table_names)}</p>
                <a href="/api/v3/migration/test-page">返回测试页面</a>
            </body></html>
            """, 404
        
        # 获取表配置信息
        table_info = manager.field_manager.get_table_info(table_name)
        
        # 创建显示配置
        table_config = {
            'display_name': _get_display_name(table_name),
            'description': f"基于API v3动态字段管理的 {table_name} 表管理页面"
        }
        
        logger.info(f"✅ API v3: 访问通用资源页面 - {table_name}")
        return render_template('resources/simple_test_v3.html',
                             table_name=table_name,
                             page_title=table_config['display_name'],
                             page_description=table_config['description'],
                             table_title=table_config['display_name'],
                             table_info=table_info)
        
    except Exception as e:
        logger.error(f"❌ API v3: 通用资源页面访问失败 - {table_name}: {e}")
        return f"""
        <html><head><title>页面加载失败</title></head>
        <body style="font-family: Arial; padding: 50px; text-align: center;">
            <h1>⚠️ 页面加载失败</h1>
            <p>错误信息: {str(e)}</p>
            <a href="/api/v3/migration/test-page">返回测试页面</a>
        </body></html>
        """, 500

# 测试页面已删除 - 现在直接使用通用页面进行测试

# 冗余路由已删除 - 现在统一使用 /api/v3/universal/<table_name>

@api_v3.route('/navigation')
def navigation_center():
    """API v3导航中心"""
    try:
        logger.info("✅ API v3: 访问导航中心")
        return render_template('resources/api_v3_navigation.html')

    except Exception as e:
        logger.error(f"❌ API v3: 导航中心失败: {e}")
        return f"页面加载失败: {e}", 500

@api_v3.route('/universal/<table_name>')
def universal_resource_page_v3(table_name):
    """通用资源管理页面v3 - 完整功能版本，使用统一配置系统"""

    try:
        # 获取表配置
        table_config = get_table_config(table_name)
        if not table_config:
            logger.warning(f"❌ API v3: 表 {table_name} 的配置未找到")
            return f"""
            <html><head><title>表配置未找到</title></head>
            <body style="font-family: Arial; padding: 50px; text-align: center;">
                <h1>❌ 表配置未找到</h1>
                <p>表 {table_name} 的配置未找到</p>
                <a href="/api/v3/navigation">返回导航中心</a>
            </body></html>
            """, 404

        # 验证表是否存在
        manager = get_enhanced_manager()
        table_info = manager.field_manager.get_table_info(table_name)

        if not table_info:
            logger.warning(f"❌ API v3: 表 {table_name} 不存在")
            return f"""
            <html><head><title>表不存在</title></head>
            <body style="font-family: Arial; padding: 50px; text-align: center;">
                <h1>❌ 表不存在</h1>
                <p>表 {table_name} 在数据库中不存在</p>
                <a href="/api/v3/navigation">返回导航中心</a>
            </body></html>
            """, 404

        logger.info(f"✅ API v3: 访问通用资源页面v3 - {table_name}")
        return render_template('resources/universal_resource_v3.html',
                             table_name=table_name,
                             page_title=table_config['title'],
                             page_icon=table_config['icon'],
                             page_description=table_config['description'],
                             table_config=table_config)

    except Exception as e:
        logger.error(f"❌ API v3: 通用资源页面v3访问失败 - {table_name}: {e}")
        return f"""
        <html><head><title>页面加载失败</title></head>
        <body style="font-family: Arial; padding: 50px; text-align: center;">
            <h1>⚠️ 页面加载失败</h1>
            <p>错误信息: {str(e)}</p>
            <a href="/api/v3/navigation">返回导航中心</a>
        </body></html>
        """, 500

@api_v3.route('/tables/<table_name>/export', methods=['GET'])
def export_table_data_v3(table_name):
    """导出表数据 - 支持Excel和CSV格式"""
    try:
        # 获取请求参数
        export_format = request.args.get('format', 'excel').lower()

        # 处理筛选条件
        filters = []
        filters_param = request.args.get('filters')
        if filters_param:
            import json
            filters = json.loads(filters_param)

        # 处理排序条件
        sort_by = request.args.get('sort_by', '')
        sort_order = request.args.get('sort_order', 'asc')

        # 处理搜索条件
        search = request.args.get('search', '')
        if search:
            filters.append({
                'field': '_global_search',
                'operator': 'contains',
                'value': search
            })

        manager = get_enhanced_manager()

        # 获取所有数据（不分页）- 使用load_all参数
        result = manager.get_table_data(table_name, page=1, per_page=10000, filters=filters, sort_by=sort_by, sort_order=sort_order, load_all=True)

        if not result['success']:
            return jsonify({
                'success': False,
                'error': result.get('error', '获取数据失败')
            }), 500

        # 获取列信息 - 修复字段名不匹配问题
        columns = result.get('columns', [])
        if not columns:
            # 如果没有columns字段，尝试从fields字段提取列名
            fields = result.get('fields', [])
            if fields:
                columns = [field.get('name', field.get('field', '')) if isinstance(field, dict) else str(field) for field in fields]
            else:
                # 最后从数据中提取列名
                if result['data']:
                    columns = list(result['data'][0].keys())
                else:
                    columns = []
        
        # 根据格式导出
        if export_format == 'csv':
            return _export_csv(table_name, columns, result['data'])
        else:
            return _export_excel(table_name, columns, result['data'])

    except Exception as e:
        logger.error(f"❌ API v3: 导出数据异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def _export_csv(table_name, columns, data):
    """导出CSV格式"""
    import csv
    import io
    from flask import Response
    from datetime import datetime

    output = io.StringIO()
    writer = csv.writer(output)

    # 写入表头
    writer.writerow(columns)

    # 写入数据
    for row in data:
        writer.writerow([row.get(col, '') for col in columns])

    output.seek(0)

    # ✅ 修复：统一文件命名规范 - 添加时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    display_name = _get_display_name(table_name)
    filename = f'{display_name}_导出_{timestamp}.csv'

    return Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={
            'Content-Disposition': f'attachment; filename={filename}'
        }
    )

def _export_excel(table_name, columns, data):
    """导出Excel格式"""
    try:
        import pandas as pd
        import io
        from flask import Response
        from datetime import datetime

        # 创建DataFrame
        df_data = []
        for row in data:
            df_data.append([row.get(col, '') for col in columns])

        df = pd.DataFrame(df_data, columns=columns)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=table_name, index=False)

        output.seek(0)

        # ✅ 修复：统一文件命名规范 - 添加时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        display_name = _get_display_name(table_name)
        filename = f'{display_name}_导出_{timestamp}.xlsx'

        return Response(
            output.getvalue(),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                'Content-Disposition': f'attachment; filename={filename}'
            }
        )

    except ImportError:
        # 如果没有pandas，回退到CSV
        logger.warning("pandas未安装，回退到CSV导出")
        return _export_csv(table_name, columns, data)

def _get_display_name(table_name):
    """获取表的显示名称"""
    display_names = {
        'et_wait_lot': '待排产批次',
        'et_ft_test_spec': '测试规范',
        'et_uph_eqp': 'UPH设备',
        'et_recipe_file': '设备配方',
        'eqp_status': '设备状态',
        'tcc_inv': '硬件库存',
        'ct': '生产周期',
        'wip_lot': '在制品',
        'lotprioritydone': '已排产批次',
        'devicepriorityconfig': '设备优先级配置',
        'lotpriorityconfig': '批次优先级配置',
        'product_priority_config': '产品优先级配置'
    }
    return display_names.get(table_name, table_name.upper())